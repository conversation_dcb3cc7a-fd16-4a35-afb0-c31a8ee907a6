import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import <PERSON>go<PERSON><PERSON>utton from '@/app/ui/logout-button'
import { Suspense } from 'react'
import FileBrowser from '@/app/ui/file-manager/file-browser'

export default async function DashboardPage() {
  // Get the session
  const session = await auth()
  
  // Log session details for debugging
  console.log("Dashboard session:", JSON.stringify({
    exists: !!session,
    hasUser: !!session?.user,
    hasUserId: !!session?.user?.id,
    userId: session?.user?.id
  }));
  
  // If no session, redirect to login
  if (!session?.user) {
    console.log("No session found in dashboard, redirecting to login");
    redirect('/login')
  }
  
  console.log("Session found in dashboard, rendering page");
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Dashboard</h1>
        <LogoutButton />
      </div>
      
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm mb-8">
        <h2 className="text-xl font-semibold mb-4">Welcome, {session.user?.name || session.user?.email}</h2>
        <p className="text-gray-600 dark:text-gray-300">
          You are now logged in to your account.
        </p>
      </div>
      
      {/* File Browser Component */}
      <Suspense fallback={<div className="p-6 text-center">Loading file browser...</div>}>
        <FileBrowser />
      </Suspense>
    </div>
  )
}
