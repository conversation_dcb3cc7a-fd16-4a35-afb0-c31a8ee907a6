'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { signIn } from '@/auth'

export default function SignupForm() {
  const router = useRouter()
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      // This is a placeholder for actual registration logic
      // In a real app, you would call an API endpoint to create the user
      
      // For demo purposes, we'll simulate a successful registration
      // and then sign in the user automatically
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // After successful registration, sign in the user
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        setError('Error signing in after registration')
        setLoading(false)
      } else {
        router.push('/dashboard')
        router.refresh()
      }
    } catch (error) {
      setError('An error occurred during registration')
      setLoading(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setLoading(true)
    try {
      await signIn('google', { 
        callbackUrl: '/dashboard'
      })
    } catch (error) {
      setError('An error occurred during Google login')
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4 max-w-md mx-auto">
      <div>
        <label htmlFor="name" className="block text-sm font-medium mb-1">Full Name</label>
        <input 
          id="name" 
          type="text" 
          value={name}
          onChange={(e) => setName(e.target.value)}
          required
          className="w-full p-2 border rounded-md"
        />
      </div>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
        <input 
          id="email" 
          type="email" 
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          className="w-full p-2 border rounded-md"
        />
      </div>
      
      <div>
        <label htmlFor="password" className="block text-sm font-medium mb-1">Password</label>
        <input 
          id="password" 
          type="password" 
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          minLength={6}
          className="w-full p-2 border rounded-md"
        />
      </div>
      
      {error && <p className="text-red-500 text-sm">{error}</p>}
      
      <button 
        type="submit" 
        disabled={loading}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
      >
        {loading ? 'Creating account...' : 'Sign up'}
      </button>

      <button
        onClick={handleGoogleSignIn}
        disabled={loading}
        className="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md transition-colors"
      >
        {loading ? 'Signing in with Google...' : 'Sign in with Google'}
      </button>
    </form>
  )
}
