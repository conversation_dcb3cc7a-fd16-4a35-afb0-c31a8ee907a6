'use client'

import { useState, useRef } from 'react'
import { getUserFilePath } from '@/app/lib/blob-storage'

type FileUploadProps = {
  onUploadComplete: () => void
}

export default function FileUpload({ onUploadComplete }: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  async function handleUpload(event: React.FormEvent) {
    event.preventDefault()
    
    const fileInput = fileInputRef.current
    if (!fileInput?.files?.length) {
      setUploadError('Please select a file')
      return
    }

    const file = fileInput.files[0]
    
    try {
      setUploading(true)
      setUploadError(null)
      
      // Get the user-specific file path
      const fileName = file.name
      const userFilePath = await getUserFilePath(fileName)
      
      // Create form data for the upload
      const formData = new FormData()
      formData.append('file', file)
      
      // Upload to Vercel Blob using the /api/upload endpoint
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })
      
      if (!response.ok) {
        throw new Error('Upload failed')
      }
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      
      // Notify parent component that upload is complete
      onUploadComplete()
      
    } catch (error) {
      console.error('Upload error:', error)
      setUploadError('Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  return (
    <div>
      <form onSubmit={handleUpload} className="flex items-center space-x-2">
        <input
          type="file"
          ref={fileInputRef}
          className="block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-full file:border-0
            file:text-sm file:font-semibold
            file:bg-blue-50 file:text-blue-700
            hover:file:bg-blue-100"
          disabled={uploading}
        />
        <button
          type="submit"
          disabled={uploading}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {uploading ? 'Uploading...' : 'Upload'}
        </button>
      </form>
      
      {uploadError && (
        <p className="mt-2 text-sm text-red-600">{uploadError}</p>
      )}
    </div>
  )
}