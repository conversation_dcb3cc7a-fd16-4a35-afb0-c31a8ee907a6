'use client'

import { useState, useEffect } from 'react'
import { getUserFiles, deleteUserFile } from '@/app/lib/blob-storage'
import FileUpload from '@/app/ui/file-manager/file-upload'
import { useRouter } from 'next/navigation'

type File = {
  id: string
  name: string
  url: string
  size: number
  uploadedAt: string
}

export default function FileBrowser() {
  const router = useRouter()
  const [files, setFiles] = useState<File[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load files on component mount
  useEffect(() => {
    loadFiles()
  }, [])

  async function loadFiles() {
    try {
      setLoading(true)
      console.log("Attempting to load user files...");
      const userFiles = await getUserFiles()
      console.log("User files loaded successfully:", userFiles.length);
      
      // Convert Date objects to strings if needed
      const formattedFiles = userFiles.map(file => ({
        ...file,
        uploadedAt: typeof file.uploadedAt === 'object' ? file.uploadedAt.toISOString() : file.uploadedAt
      }))
      setFiles(formattedFiles)
      setError(null)
    } catch (err) {
      console.error('Failed to load files:', err)
      // Redirect to login if not authenticated
      if (err instanceof Error && err.message === 'User not authenticated') {
        console.log("Authentication error detected, redirecting to login");
        router.push('/login')
      } else {
        setError('Failed to load files')
      }
    } finally {
      setLoading(false)
    }
  }

  async function handleDelete(fileId: string) {
    if (confirm('Are you sure you want to delete this file?')) {
      try {
        setLoading(true);
        console.log(`Attempting to delete file: ${fileId}`);
        await deleteUserFile(fileId);
        console.log(`File deleted successfully: ${fileId}`);
        
        // Refresh the file list
        await loadFiles();
      } catch (err) {
        console.error('Failed to delete file:', err);
        setError('Failed to delete file');
        
        // Handle authentication errors
        if (err instanceof Error && err.message === 'User not authenticated') {
          console.log("Authentication error detected during delete, redirecting to login");
          router.push('/login');
        }
      } finally {
        setLoading(false);
      }
    }
  }

  function formatFileSize(bytes: number) {
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
    else return (bytes / 1048576).toFixed(1) + ' MB'
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Your Documents</h2>
        <FileUpload onUploadComplete={loadFiles} />
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : error ? (
        <div className="text-red-500 py-4">{error}</div>
      ) : files.length === 0 ? (
        <div className="text-center py-8 text-gray-500 dark:text-gray-400">
          <p>You haven't uploaded any files yet.</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Size</th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Uploaded</th>
                <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {files.map((file) => (
                <tr key={file.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <a 
                      href={file.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      {file.name}
                    </a>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatFileSize(file.size)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {new Date(file.uploadedAt).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleDelete(file.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}
