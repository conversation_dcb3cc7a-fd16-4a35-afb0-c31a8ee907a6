'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { signOut } from 'next-auth/react'

export default function Nav({ user }: { user: { name?: string; email?: string } | null }) {
  const pathname = usePathname()
  
  return (
    <nav className="container mx-auto px-6 py-4">
      <div className="flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold">InsureDoc</Link>
        
        <div className="flex items-center gap-6">
          <Link 
            href="/features" 
            className={`${pathname === '/features' ? 'text-blue-600' : 'text-gray-700 dark:text-gray-300'} hover:text-blue-600`}
          >
            Features
          </Link>
          <Link 
            href="/pricing" 
            className={`${pathname === '/pricing' ? 'text-blue-600' : 'text-gray-700 dark:text-gray-300'} hover:text-blue-600`}
          >
            Pricing
          </Link>
          
          {user ? (
            <div className="flex items-center gap-4">
              <Link 
                href="/dashboard" 
                className="rounded-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 font-medium transition-colors"
              >
                Dashboard
              </Link>
              <button
                onClick={() => signOut({ callbackUrl: '/' })}
                className="text-gray-700 dark:text-gray-300 hover:text-red-600"
              >
                Log out
              </button>
            </div>
          ) : (
            <div className="flex items-center gap-4">
              <Link 
                href="/login" 
                className="text-gray-700 dark:text-gray-300 hover:text-blue-600 font-medium"
              >
                Log in
              </Link>
              <Link 
                href="/signup" 
                className="rounded-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 font-medium transition-colors"
              >
                Sign up
              </Link>
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}
