import { NextRequest } from 'next/server';
import { handlers } from '@/auth'

// Log when this route is hit
console.log('API auth route handler loaded');

export const GET = async (req: NextRequest) => {
  console.log('API auth GET request received:', req.url);
  return handlers.GET(req);
}

export const POST = async (req: NextRequest) => {
  console.log('API auth POST request received:', req.url);
  return handlers.POST(req);
}
