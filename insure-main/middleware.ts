import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

// IMPORTANT: Don't use auth() in middleware
// Middleware runs on the Edge runtime which may not be compatible with auth()
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl
  
  // Skip middleware for all authentication-related paths
  if (
    pathname.startsWith('/api/auth') || 
    pathname.includes('/auth/callback') ||
    pathname === '/login' ||
    pathname === '/signup' ||
    pathname === '/' ||
    pathname.startsWith('/_next')
  ) {
    return NextResponse.next()
  }
  
  // For now, just pass through all requests
  // We'll handle auth in the page components instead
  return NextResponse.next()
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|.*\\.png$).*)',
  ],
}
